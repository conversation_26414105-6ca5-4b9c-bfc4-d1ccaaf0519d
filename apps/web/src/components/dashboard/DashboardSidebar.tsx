import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { cn } from "@/lib/utils";
import {
    Bar<PERSON>hart,
    Bell, Building2, Calendar,
    ChevronLeft,
    ChevronRight,
    ClipboardList,
    FileText,
    LayoutDashboard,
    MessageSquare,
    Pill,
    Settings,
    Users
} from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";
import { Logo } from "../ui/logo";

interface DashboardSidebarProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  mobileMenuOpen: boolean;
  setMobileMenuOpen: (open: boolean) => void;
  screenSize: 'mobile' | 'tablet' | 'desktop';
}

export function DashboardSidebar({
  open,
  setOpen,
  mobileMenuOpen,
  setMobileMenuOpen,
  screenSize
}: DashboardSidebarProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { organization } = useAuth();
  const { isSystemAdmin, isOrgAdmin } = useUserRoles();

  // Check if user has admin permissions
  const canManageOrgs = isSystemAdmin || isOrgAdmin;

  // Base navigation items
  const baseNavItems = [
    {
      name: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      name: "Patients",
      href: "/patients",
      icon: Users,
    },
    {
      name: "Appointments",
      href: "/appointments",
      icon: Calendar,
    },
    {
      name: "Medical Records",
      href: "/medical-records",
      icon: ClipboardList,
    },
    {
      name: "Medications",
      href: "/medications",
      icon: Pill,
    },
    {
      name: "Documents",
      href: "/documents",
      icon: FileText,
    },
    {
      name: "Messages",
      href: "/messages",
      icon: MessageSquare,
    },
    {
      name: "Notifications",
      href: "/notifications",
      icon: Bell,
    },
    {
      name: "Analytics",
      href: "/analytics",
      icon: BarChart,
    },
  ];

  // Admin-only navigation items
  const adminNavItems = [
    {
      name: "Organizations",
      href: "/organizations/manage",
      icon: Building2,
    },
  ];

  // Combine navigation items based on permissions
  const navItems = [
    ...baseNavItems,
    ...(canManageOrgs ? adminNavItems : []),
    {
      name: "Settings",
      href: "/settings",
      icon: Settings,
    },
  ];

  // Mobile sidebar (overlay)
  if (screenSize === 'mobile') {
    return (
      <>
        {mobileMenuOpen && (
          <div className="fixed inset-0 z-50 lg:hidden">
            <div className="fixed inset-0 bg-black/50" onClick={() => setMobileMenuOpen(false)} />
            <div className="fixed left-0 top-0 h-full w-64 bg-sidebar border-r border-border flex flex-col">
              {/* Mobile header with close button */}
              <div className="p-4 border-b border-border flex items-center justify-between">
                <Logo size="md" showText={true} className="flex-shrink-0" />
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <ChevronLeft size={18} />
                </Button>
              </div>

              {/* Organization name */}
              {organization && (
                <div className="px-4 py-3 border-b border-border">
                  <div>
                    <p className="font-medium text-sm">{organization.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {organization.type || "Healthcare Organization"}
                    </p>
                  </div>
                </div>
              )}

              {/* Navigation */}
              <nav className="flex-1 overflow-y-auto py-4">
                <ul className="space-y-1 px-2">
                  {navItems.map((item) => {
                    const isActive = location.pathname === item.href;
                    return (
                      <li key={item.href}>
                        <Button
                          variant={isActive ? "default" : "ghost"}
                          className={cn(
                            "w-full justify-start",
                            isActive && "bg-primary text-primary-foreground",
                          )}
                          onClick={() => {
                            navigate(item.href);
                            setMobileMenuOpen(false);
                          }}
                        >
                          <item.icon className="h-5 w-5 mr-2" />
                          <span>{item.name}</span>
                        </Button>
                      </li>
                    );
                  })}
                </ul>
              </nav>

              {/* Footer */}
              <div className="p-4 border-t border-border">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    navigate("/help");
                    setMobileMenuOpen(false);
                  }}
                >
                  <Bell className="h-5 w-5 mr-2" />
                  <span>Help & Support</span>
                </Button>
              </div>
            </div>
          </div>
        )}
      </>
    );
  }

  // Desktop/Tablet sidebar
  return (
    <div
      className={cn(
        "h-screen bg-sidebar border-r border-border transition-all duration-300 flex flex-col flex-shrink-0",
        screenSize === 'desktop' && (open ? "w-64" : "w-20"),
        screenSize === 'tablet' && (open ? "w-64" : "w-16"),
      )}
    >
      {/* Logo and toggle */}
      <div className="p-4 border-b border-border flex items-center justify-between">
        <Logo
          size="md"
          showText={open}
          className="flex-shrink-0"
        />
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={() => setOpen(!open)}
        >
          {open ? <ChevronLeft size={18} /> : <ChevronRight size={18} />}
        </Button>
      </div>

      {/* Organization name */}
      {organization && (
        <div
          className={cn(
            "px-4 py-3 border-b border-border",
            !open && "flex justify-center",
          )}
        >
          {open ? (
            <div>
              <p className="font-medium text-sm">{organization.name}</p>
              <p className="text-xs text-muted-foreground">
                {organization.type || "Healthcare Organization"}
              </p>
            </div>
          ) : (
            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
              <span className="text-xs font-medium text-primary">
                {organization.name.charAt(0)}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto py-4">
        <ul className="space-y-1 px-2">
          {navItems.map((item) => {
            const isActive = location.pathname === item.href;
            return (
              <li key={item.href}>
                <Button
                  variant={isActive ? "default" : "ghost"}
                  className={cn(
                    "w-full justify-start",
                    !open && "justify-center px-0",
                    isActive && "bg-primary text-primary-foreground",
                  )}
                  onClick={() => navigate(item.href)}
                >
                  <item.icon className={cn("h-5 w-5", open && "mr-2")} />
                  {open && <span>{item.name}</span>}
                </Button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        <Button
          variant="outline"
          className={cn("w-full", !open && "justify-center px-0")}
          onClick={() => navigate("/help")}
        >
          <Bell className={cn("h-5 w-5", open && "mr-2")} />
          {open && <span>Help & Support</span>}
        </Button>
      </div>
    </div>
  );
}
