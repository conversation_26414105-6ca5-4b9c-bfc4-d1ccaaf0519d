import { AppointmentFilters } from "@/components/appointments/AppointmentFilters";
import { AppointmentStats } from "@/components/appointments/AppointmentStats";
import { AppointmentTable } from "@/components/appointments/AppointmentTable";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataPagination } from "@/components/ui/data-pagination";
import { useAppointments } from "@/hooks/dashboard/useAppointments";
import { useAuth } from "@/hooks/useAuth";
import { usePagination } from "@/hooks/usePagination";
import { useUserRoles } from "@/hooks/useUserRoles";
import { TABLE_STORAGE_KEYS } from "@/lib/table-preferences";
import { AppointmentFilters as AppointmentFiltersType } from "@/types/appointment";
import { Calendar, Plus } from "lucide-react";
import { useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";

export function AppointmentsManagePage() {
  const { organization } = useAuth();
  const { isSystemAdmin } = useUserRoles();
  const navigate = useNavigate();

  const [filters, setFilters] = useState<AppointmentFiltersType>({
    search: "",
    status: "all",
    dateRange: undefined,
    providerId: undefined,
    departmentId: undefined,
  });

  // Pagination setup with configurable page size and localStorage persistence
  const {
    currentPage,
    offset,
    pageSize,
    itemsPerPage,
    goToPage,
    setPageSize,
  } = usePagination({
    totalCount: 0, // Will be updated after we get the data
    allowPageSizeChange: true,
    defaultPageSize: 10,
    storageKey: TABLE_STORAGE_KEYS.APPOINTMENTS,
  });

  // Memoize appointment options to prevent unnecessary rerenders
  const appointmentOptions = useMemo(() => ({
    status: filters.status !== "all" ? filters.status as any : undefined,
    dateRange: filters.dateRange,
    limit: itemsPerPage,
    offset,
  }), [filters.status, filters.dateRange, itemsPerPage, offset]);

  // Single optimized API call for both appointments and totalCount
  const { appointments, isLoading, totalCount } = useAppointments(appointmentOptions);

  // Update pagination when totalCount changes
  const actualTotalPages = Math.ceil(totalCount / itemsPerPage);

  const handleFiltersChange = (newFilters: AppointmentFiltersType) => {
    setFilters(newFilters);
    goToPage(1); // Reset to first page when filters change
  };

  const handleCreateAppointment = () => {
    navigate("/appointments/new");
  };

  const getPageTitle = () => {
    if (isSystemAdmin && organization?.id === "system-admin-all-orgs") {
      return "All Appointments";
    }
    return `${organization?.name || "Organization"} Appointments`;
  };

  const getPageDescription = () => {
    if (isSystemAdmin && organization?.id === "system-admin-all-orgs") {
      return "Manage appointments across all organizations";
    }
    return "Manage and view appointment schedules for your organization";
  };

  return (
    <div className="space-y-6 w-full px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold">{getPageTitle()}</h1>
          <p className="text-muted-foreground mt-1">{getPageDescription()}</p>
        </div>
        <Button onClick={handleCreateAppointment} className="w-full sm:w-auto">
          <Plus className="mr-2 h-4 w-4" />
          <span className="sm:hidden">Schedule</span>
          <span className="hidden sm:inline">Schedule Appointment</span>
        </Button>
      </div>

      {/* Stats */}
      <AppointmentStats
        appointments={appointments}
        totalCount={totalCount}
        isLoading={isLoading}
      />

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Appointment Schedule
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Filters */}
          <AppointmentFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            organization={organization}
            isSystemAdmin={isSystemAdmin}
          />

          {/* Table */}
          <AppointmentTable
            appointments={appointments}
            isLoading={isLoading}
            organization={organization}
            isSystemAdmin={isSystemAdmin}
          />

          {/* Pagination */}
          <DataPagination
            currentPage={currentPage}
            totalPages={actualTotalPages}
            onPageChange={goToPage}
            pageSize={pageSize}
            onPageSizeChange={setPageSize}
            totalCount={totalCount}
            showPageSizeSelector={true}
            showTotalCount={true}
            totalCountLabel="appointment"
          />
        </CardContent>
      </Card>
    </div>
  );
}
