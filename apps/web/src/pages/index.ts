// Appointment pages
export { AppointmentsManagePage } from "./appointments/AppointmentsManagePage";
// Auth pages
export { ForgotPasswordForm as ForgotPasswordPage } from "@/components/auth/ForgotPasswordForm";
export { LoginForm as LoginPage } from "@/components/auth/LoginForm";
export { RegisterForm as RegisterPage } from "@/components/auth/RegisterForm";
export { ResetPasswordForm as ResetPasswordPage } from "@/components/auth/ResetPasswordForm";
export { default as PatientsPage } from "@/components/patients/Patients";
export { default as SettingsPage } from "@/components/settings/Settings";
// Feature pages
export { DashboardPage } from "@/pages/dashboard/DashboardPage";
// Error pages
export { NotFoundPage } from "./error/NotFoundPage";
// Home page
export { HomePage } from "./home/<USER>";
// Organization pages
export { OrganizationSettingsPage } from "./organizations/OrganizationSettingsPage";
export { OrganizationsManagePage } from "./organizations/OrganizationsManagePage";
export { OrganizationsPage } from "./organizations/OrganizationsPage";
// Patient pages
export { PatientDetailsPage } from "./patients/PatientDetailsPage";
export { PatientEditPage } from "./patients/PatientEditPage";
export { PatientsManagePage } from "./patients/PatientsManagePage";
export { FullSetupPage } from "./setup/FullSetupPage";
// Setup pages
export { SetupRouter } from "./setup/SetupRouter";
