import { PatientFilters } from "@/components/patients/PatientFilters";
import { PatientStats } from "@/components/patients/PatientStats";
import { PatientTable } from "@/components/patients/PatientTable";
import { Button } from "@/components/ui/button";
import { Card, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { DataPagination } from "@/components/ui/data-pagination";
import { usePatients } from "@/hooks/patients/usePatients";
import { useAuth } from "@/hooks/useAuth";
import { usePagination } from "@/hooks/usePagination";
import { useUserRoles } from "@/hooks/useUserRoles";
import { TABLE_STORAGE_KEYS } from "@/lib/table-preferences";
import { PatientFilters as PatientFiltersType } from "@/types/patient";
import { Plus, Users } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

export function PatientsManagePage() {
  const { organization } = useAuth();
  const { isSystemAdmin } = useUserRoles();
  const navigate = useNavigate();

  const [filters, setFilters] = useState<PatientFiltersType>({
    search: "",
    status: "all",
    gender: "all",
  });

  // Get patients with pagination first to get totalCount
  const { totalCount } = usePatients({
    filters,
    limit: 10, // Use default page size initially
    offset: 0,
    includeStats: false,
    includeOrganization:
      isSystemAdmin && organization?.id === "system-admin-all-orgs",
  });

  // Pagination setup with configurable page size and localStorage persistence
  const {
    currentPage,
    offset,
    pageSize,
    itemsPerPage,
    goToPage,
    setPageSize,
  } = usePagination({
    totalCount, // Pass the actual totalCount
    allowPageSizeChange: true,
    defaultPageSize: 10,
    storageKey: TABLE_STORAGE_KEYS.PATIENTS, // Unique key for patients table
  });

  // Get patients with updated pagination parameters
  const { patients: paginatedPatients, isLoading: isLoadingPaginated } = usePatients({
    filters,
    limit: itemsPerPage,
    offset,
    includeStats: false,
    includeOrganization:
      isSystemAdmin && organization?.id === "system-admin-all-orgs",
  });

  // Update pagination when totalCount changes
  const actualTotalPages = Math.ceil(totalCount / itemsPerPage);

  const handleFiltersChange = (newFilters: PatientFiltersType) => {
    setFilters(newFilters);
    goToPage(1); // Reset to first page when filters change
  };

  const handleCreatePatient = () => {
    navigate("/patients/new");
  };

  const getPageTitle = () => {
    if (isSystemAdmin && organization?.id === "system-admin-all-orgs") {
      return "All Patients";
    }
    return `${organization?.name || "Organization"} Patients`;
  };

  const getPageDescription = () => {
    if (isSystemAdmin && organization?.id === "system-admin-all-orgs") {
      return "Manage patients across all organizations";
    }
    return "Manage and view patient information for your organization";
  };

  return (
    <div className="space-y-6 w-full">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold">{getPageTitle()}</h1>
          <p className="text-muted-foreground mt-1">{getPageDescription()}</p>
        </div>
        <Button onClick={handleCreatePatient} className="w-full sm:w-auto">
          <Plus className="mr-2 h-4 w-4" />
          Add Patient
        </Button>
      </div>

      {/* Stats */}
      <PatientStats patients={paginatedPatients} totalCount={totalCount} isLoading={isLoadingPaginated} />

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Patient Directory
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Filters */}
          <PatientFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            totalCount={totalCount}
          />

          {/* Table */}
          <PatientTable patients={paginatedPatients} isLoading={isLoadingPaginated} />

          {/* Pagination */}
          <DataPagination
            currentPage={currentPage}
            totalPages={actualTotalPages}
            onPageChange={goToPage}
            pageSize={pageSize}
            onPageSizeChange={setPageSize}
            totalCount={totalCount}
            showPageSizeSelector={true}
            showTotalCount={true}
            totalCountLabel="patient"
          />
        </CardContent>
      </Card>
    </div>
  );
}
