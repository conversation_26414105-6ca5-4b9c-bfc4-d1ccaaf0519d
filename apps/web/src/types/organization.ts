import { Organization } from "@/contexts/auth-context-types";

export interface OrganizationWithHierarchy extends Organization {
  parent_id?: string;
  hierarchy_level?: number;
  hierarchy_path?: string;
  children?: OrganizationWithHierarchy[];
  location_count?: number;
}

export interface OrganizationTreeNode extends OrganizationWithHierarchy {
  children: OrganizationTreeNode[];
  ancestors?: OrganizationWithHierarchy[];
  descendants?: OrganizationWithHierarchy[];
}

export interface OrganizationHierarchyStats {
  totalOrganizations: number;
  maxDepth: number;
  rootOrganizations: number;
  organizationsWithLocations: number;
  totalLocations: number;
}

export interface UserOrganizationAccess {
  organizationId: string;
  role: string;
  permissions: string[];
  isActive: boolean;
}

export interface OrganizationCache {
  organizations: Organization[];
  currentOrgId: string;
  timestamp: number;
  userId: string;
}